import 'package:audioplayers/audioplayers.dart';
import 'package:just_audio/just_audio.dart' as just_audio;

class GameAssets {
  static const alarm = 'assets/images/alarm.svg';
  static const bookFrontFace = 'assets/images/front_face_book.svg';
  static const book = 'assets/images/book.svg';
  static const chair = 'assets/images/chair.svg';
  static const eraser = 'assets/images/eraser.svg';
  static const light = 'assets/images/light.svg';
  static const message = 'assets/images/message.svg';
  static const pencil = 'assets/images/pencil.svg';
  static const ruler = 'assets/images/ruler.svg';
  static const coin = 'assets/images/coin.svg';
  static const studyDesk = 'assets/images/table.svg';
  static const gameOver = 'assets/images/game_over.svg';
  static const victory = 'assets/images/victory.svg';
  static const star = 'assets/images/star.svg';
  static const _turnOnSound = 'sounds/light_turn_on.mp3';
  static const _turnOffSound = 'sounds/light_turn_off.mp3';
  static const _clockTickingSound = 'sounds/clock_ticking.mp3';
  static const _backgroundMusicSound = 'sounds/background_music.mp3';
  static const _gameOverSound = 'sounds/game_over.mp3';
  static const _victorySound = 'sounds/victory.mp3';
  static const _earnCoinsSound = 'sounds/earn_coins.mp3';

  static late final AudioPlayer turnOnPlayer;
  static late final AudioPlayer turnOffPlayer;
  static late final AudioPlayer clockTickingPlayer;
  static late final AudioPlayer gameOverPlayer;
  static late final AudioPlayer victoryPlayer;
  static late final AudioPlayer earnCoinsPlayer;

  static late final just_audio.AudioPlayer backgroundPlayer;

  static Future<void> preloadAssets() async {
    await _preloadImageAssets();
    await _preloadAudioAssets();
  }

  static Future<void> _preloadAudioAssets() async {
    // Initialize all audio players
    backgroundPlayer = just_audio.AudioPlayer();
    turnOnPlayer = AudioPlayer();
    turnOffPlayer = AudioPlayer();
    clockTickingPlayer = AudioPlayer();
    gameOverPlayer = AudioPlayer();
    victoryPlayer = AudioPlayer();
    earnCoinsPlayer = AudioPlayer();

    // Set up background music
    await backgroundPlayer.setAsset('assets/$_backgroundMusicSound');
    await backgroundPlayer.setLoopMode(just_audio.LoopMode.one);

    // Preload sound effects
    await turnOnPlayer.setSource(AssetSource(_turnOnSound));
    await turnOffPlayer.setSource(AssetSource(_turnOffSound));
    await clockTickingPlayer.setSource(AssetSource(_clockTickingSound));
    await gameOverPlayer.setSource(AssetSource(_gameOverSound));
    await victoryPlayer.setSource(AssetSource(_victorySound));
    await earnCoinsPlayer.setSource(AssetSource(_earnCoinsSound));
  }

  // Helper methods for playing sounds
  static Future<void> playTurnOnSound() async {
    await turnOnPlayer.resume();
  }

  static Future<void> playTurnOffSound() async {
    await turnOffPlayer.resume();
  }

  static Future<void> playClockTickingSound() async {
    await clockTickingPlayer.resume();
  }

  static Future<void> playGameOverSound() async {
    await gameOverPlayer.resume();
  }

  static Future<void> playVictorySound() async {
    await victoryPlayer.resume();
  }

  static Future<void> playEarnCoinsSound() async {
    await earnCoinsPlayer.resume();
  }

  static Future<void> stopAllSounds() async {
    await turnOnPlayer.stop();
    await turnOffPlayer.stop();
    await clockTickingPlayer.stop();
    await gameOverPlayer.stop();
    await victoryPlayer.stop();
    await earnCoinsPlayer.stop();
  }

  static Future<void> _preloadImageAssets() async {
    // SVG assets will be loaded on demand
    // This method is kept for future implementation if needed
  }
}
