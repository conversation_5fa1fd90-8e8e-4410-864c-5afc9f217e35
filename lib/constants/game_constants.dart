import 'package:brain_game/controllers/game_mode_controller.dart';
import 'package:get/get.dart';

class GameCosntants {
  // Dynamic values based on selected game mode
  static int get numberOfLevels {
    try {
      return GameModeController.to.currentLevels;
    } catch (e) {
      return 10; // Default fallback
    }
  }

  static int get startTimeInMilliseconds {
    try {
      return GameModeController.to.currentTimeLimit;
    } catch (e) {
      return 300; // Default fallback
    }
  }

  static int get minNumberOfSwaps {
    try {
      return GameModeController.to.currentMinSwaps;
    } catch (e) {
      return 4; // Default fallback
    }
  }

  static int get maxNumberOfSwaps {
    try {
      return GameModeController.to.currentMaxSwaps;
    } catch (e) {
      return 7; // Default fallback
    }
  }

  // Static constants that don't change
  static const int numberOfRoundsBerLevel = 3;
  static const int timeToDecrementInMilliseconds = 28;
  static const List<int> minNumberOfSwapsBerLevel = [4, 6, 8];
  static const List<int> maxNumberOfSwapsBerLevelArray = [5, 7, 9];
  static const List<String> ranks = [
    'No Comment',
    'Not Bad',
    'Good!',
    'Very Good!',
    'Excellent!',
    'Perfect!',
  ];
}
