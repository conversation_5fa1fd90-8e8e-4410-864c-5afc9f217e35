import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class OrientationWrapper extends StatefulWidget {
  final Widget child;
  final List<DeviceOrientation> allowedOrientations;

  const OrientationWrapper({
    Key? key,
    required this.child,
    required this.allowedOrientations,
  }) : super(key: key);

  @override
  State<OrientationWrapper> createState() => _OrientationWrapperState();
}

class _OrientationWrapperState extends State<OrientationWrapper> {
  @override
  void initState() {
    super.initState();
    _setOrientation();
  }

  @override
  void didUpdateWidget(OrientationWrapper oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.allowedOrientations != widget.allowedOrientations) {
      _setOrientation();
    }
  }

  void _setOrientation() {
    SystemChrome.setPreferredOrientations(widget.allowedOrientations);
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
