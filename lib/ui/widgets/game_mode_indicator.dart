import 'package:flutter/material.dart';
import 'package:brain_game/controllers/game_mode_controller.dart';
import 'package:brain_game/utils/game_colors.dart';
import 'package:get/get.dart';

class GameModeIndicator extends StatelessWidget {
  const GameModeIndicator({Key? key, required this.width}) : super(key: key);

  final double width;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<GameModeController>(
      builder: (controller) {
        final mode = controller.selectedMode;

        return OrientationBuilder(
          builder: (context, orientation) {
            bool isLandscape = orientation == Orientation.landscape;

            return Positioned(
              top: isLandscape ? 10 : 20,
              right: isLandscape ? 10 : 20,
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: isLandscape ? 8 : 12,
                  vertical: isLandscape ? 6 : 8,
                ),
                decoration: BoxDecoration(
                  color: mode.color.withOpacity(0.9),
                  borderRadius: BorderRadius.circular(isLandscape ? 15 : 20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      mode.icon,
                      size: width * (isLandscape ? 0.025 : 0.03),
                      color: Colors.white,
                    ),
                    SizedBox(width: isLandscape ? 4 : 6),
                    Text(
                      mode.name,
                      style: TextStyle(
                        fontSize: width * (isLandscape ? 0.02 : 0.025),
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        fontFamily: 'Sansita',
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }
}
