import 'package:flutter/material.dart';
import 'package:brain_game/enums/game_state.dart';
import 'package:brain_game/controllers/game_state_controller.dart';
import 'package:brain_game/utils/game_colors.dart';
import 'package:brain_game/utils/game_assets.dart';

class MenuButton extends StatelessWidget {
  const MenuButton({Key? key, required this.width}) : super(key: key);

  final double width;

  @override
  Widget build(BuildContext context) {
    return OrientationBuilder(
      builder: (context, orientation) {
        bool isLandscape = orientation == Orientation.landscape;

        return Positioned(
          top: isLandscape ? 10 : 20,
          left: isLandscape ? 10 : 20,
          child: GestureDetector(
            onTap: () => _showExitDialog(context),
            child: Container(
              width: width * (isLandscape ? 0.06 : 0.08),
              height: width * (isLandscape ? 0.06 : 0.08),
              constraints: const BoxConstraints(minWidth: 40, minHeight: 40),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.9),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.2),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Icon(
                Icons.home,
                size: width * (isLandscape ? 0.03 : 0.04),
                color: GameColors.green,
              ),
            ),
          ),
        );
      },
    );
  }

  void _showExitDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
          title: const Text(
            'Return to menu?',
            style: TextStyle(
              fontFamily: 'Sansita',
              fontWeight: FontWeight.bold,
              color: GameColors.grey,
            ),
          ),
          content: SingleChildScrollView(
            child: const Text(
              'Are you sure you want to return to menu? Current game progress will be lost.',
              style: TextStyle(fontFamily: 'Sansita', color: GameColors.grey),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text(
                'Cancel',
                style: TextStyle(fontFamily: 'Sansita', color: GameColors.grey),
              ),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await _exitToMenu();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: GameColors.green,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              child: const Text(
                'Return',
                style: TextStyle(fontFamily: 'Sansita', color: Colors.white),
              ),
            ),
          ],
        );
      },
    );
  }

  Future<void> _exitToMenu() async {
    // Stop background music
    await GameAssets.backgroundPlayer.stop();

    // Stop all sound effects
    await GameAssets.stopAllSounds();

    // Play menu sound
    await GameAssets.playEarnCoinsSound();

    // Go back to menu
    GameStateController.to.updateState(GameState.menu);
  }
}
