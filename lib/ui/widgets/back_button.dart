import 'package:flutter/material.dart';
import 'package:brain_game/controllers/game_state_controller.dart';
import 'package:brain_game/enums/game_state.dart';
import 'package:brain_game/utils/game_colors.dart';
import 'package:brain_game/utils/game_assets.dart';

class GameBackButton extends StatelessWidget {
  const GameBackButton({Key? key, required this.width}) : super(key: key);

  final double width;

  @override
  Widget build(BuildContext context) {
    return OrientationBuilder(
      builder: (context, orientation) {
        bool isLandscape = orientation == Orientation.landscape;

        return GestureDetector(
          onTap: () => _showExitDialog(context),
          child: Container(
            width: width * (isLandscape ? 0.06 : 0.08),
            height: width * (isLandscape ? 0.06 : 0.08),
            constraints: const BoxConstraints(minWidth: 40, minHeight: 40),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.9),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Icon(
              Icons.arrow_back,
              size: width * (isLandscape ? 0.03 : 0.04),
              color: GameColors.redAccent,
            ),
          ),
        );
      },
    );
  }

  void _showExitDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          backgroundColor: Colors.white,
          title: Row(
            children: [
              Icon(Icons.warning_amber_rounded, color: Colors.orange, size: 28),
              const SizedBox(width: 10),
              const Text(
                'Exit Game?',
                style: TextStyle(
                  fontFamily: 'Sansita',
                  fontWeight: FontWeight.bold,
                  color: GameColors.grey,
                  fontSize: 20,
                ),
              ),
            ],
          ),
          content: SingleChildScrollView(
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 10),
              child: const Text(
                'Are you sure you want to exit the game?\n\nCurrent progress will be lost and you will return to the main menu.',
                style: TextStyle(
                  fontFamily: 'Sansita',
                  color: GameColors.grey,
                  fontSize: 16,
                  height: 1.4,
                ),
              ),
            ),
          ),
          actions: [
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                        side: BorderSide(
                          color: GameColors.grey.withOpacity(0.3),
                        ),
                      ),
                    ),
                    child: const Text(
                      'Continue Playing',
                      style: TextStyle(
                        fontFamily: 'Sansita',
                        color: GameColors.grey,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () async {
                      Navigator.of(context).pop();
                      await _exitToMenu();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: GameColors.redAccent,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                      elevation: 3,
                    ),
                    child: const Text(
                      'Exit Game',
                      style: TextStyle(
                        fontFamily: 'Sansita',
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  Future<void> _exitToMenu() async {
    try {
      // Play exit sound
      await GameAssets.playEarnCoinsSound();

      // Stop background music
      await GameAssets.backgroundPlayer.stop();

      // Stop all sound effects
      await GameAssets.stopAllSounds();

      // Small delay for sound effect
      await Future.delayed(const Duration(milliseconds: 200));

      // Go back to menu
      GameStateController.to.updateState(GameState.menu);

      // Restart background music
      await GameAssets.backgroundPlayer.seek(Duration.zero);
      await GameAssets.backgroundPlayer.play();
    } catch (e) {
      // Fallback: just go to menu
      GameStateController.to.updateState(GameState.menu);
    }
  }
}
