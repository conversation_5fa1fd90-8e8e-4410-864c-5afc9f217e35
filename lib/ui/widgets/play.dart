import 'package:flutter/material.dart';
import 'package:brain_game/enums/widget_name.dart';
import 'package:brain_game/ui/widgets/alram_clock.dart';
import 'package:brain_game/ui/widgets/books_container.dart';
import 'package:brain_game/ui/widgets/chair.dart';
import 'package:brain_game/ui/widgets/light.dart';
import 'package:brain_game/ui/widgets/score.dart';
import 'package:brain_game/ui/widgets/stationery.dart';
import 'package:brain_game/ui/widgets/study_desk.dart';
import 'package:brain_game/ui/widgets/study_light.dart';
import 'package:brain_game/ui/widgets/menu_button.dart';
import 'package:brain_game/ui/widgets/game_mode_indicator.dart';
import 'package:brain_game/ui/widgets/back_button.dart';
import 'package:brain_game/utils/game_colors.dart';

class Play extends StatelessWidget {
  const Play({
    Key? key,
    required this.widthOfScreen,
    required this.heightOfScreen,
    required Map<Enum, GlobalKey<State<StatefulWidget>>> stateKeysMap,
  }) : _stateKeysMap = stateKeysMap,
       super(key: key);

  final double widthOfScreen;
  final double heightOfScreen;
  final Map<Enum, GlobalKey<State<StatefulWidget>>> _stateKeysMap;

  @override
  Widget build(BuildContext context) {
    return OrientationBuilder(
      builder: (context, orientation) {
        // Determine if we're in landscape mode
        bool isLandscape = orientation == Orientation.landscape;

        // Adjust dimensions based on orientation
        double gameWidth = isLandscape ? heightOfScreen : widthOfScreen;
        double gameHeight = isLandscape ? widthOfScreen : heightOfScreen;

        return Stack(
          alignment: AlignmentDirectional.center,
          children: [
            Positioned.fill(
              child: Column(
                children: [
                  Expanded(
                    child: Container(color: GameColors.primary),
                    flex: 3,
                  ),
                  Expanded(
                    child: Container(color: GameColors.secondary),
                    flex: 2,
                  ),
                ],
              ),
            ),
            SizedBox(
              width: gameWidth,
              height: gameHeight,
              child: Stack(
                alignment: AlignmentDirectional.center,
                children: [
                  StudyDesk(width: gameWidth),
                  Stationery(width: gameWidth),
                  BooksContainer(width: gameWidth, stateKeysMap: _stateKeysMap),
                  Chair(width: gameWidth),
                  Light(width: gameWidth, key: _stateKeysMap[WidgetName.light]),
                  StudyLight(
                    width: gameWidth,
                    lightStateKey: _stateKeysMap[WidgetName.light],
                  ),

                  AlarmClock(
                    width: gameWidth,
                    key: _stateKeysMap[WidgetName.alarmClock],
                  ),
                  Score(width: gameWidth, key: _stateKeysMap[WidgetName.score]),
                  MenuButton(width: gameWidth),
                  GameModeIndicator(width: gameWidth),
                  GameBackButton(width: gameWidth),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}
