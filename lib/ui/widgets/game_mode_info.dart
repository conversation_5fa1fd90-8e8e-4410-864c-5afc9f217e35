import 'package:flutter/material.dart';
import 'package:brain_game/models/game_mode.dart';
import 'package:brain_game/utils/game_colors.dart';
import 'package:get/get.dart';
import 'package:brain_game/controllers/game_mode_controller.dart';

class GameModeInfo extends StatelessWidget {
  const GameModeInfo({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<GameModeController>(
      builder: (controller) {
        final mode = controller.selectedMode;
        return Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(15),
            boxShadow: [
              BoxShadow(
                color: mode.color.withOpacity(0.3),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: mode.color.withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(mode.icon, color: mode.color, size: 30),
                  ),
                  const SizedBox(width: 15),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Mode: ${mode.name}',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: GameColors.grey,
                            fontFamily: 'Sansita',
                          ),
                        ),
                        Text(
                          mode.description,
                          style: TextStyle(
                            fontSize: 14,
                            color: GameColors.grey.withOpacity(0.7),
                            fontFamily: 'Sansita',
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              _buildInfoRow('Số level:', '${mode.levels}', Icons.layers),
              const SizedBox(height: 10),
              _buildInfoRow('Thời gian:', '${mode.timeLimit}ms', Icons.timer),
              const SizedBox(height: 10),
              _buildInfoRow(
                'Độ khó:',
                '${mode.minSwaps}-${mode.maxSwaps} swaps',
                Icons.swap_horiz,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildInfoRow(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, size: 20, color: GameColors.green),
        const SizedBox(width: 10),
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: GameColors.grey,
            fontFamily: 'Sansita',
          ),
        ),
        const Spacer(),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: GameColors.green,
            fontFamily: 'Sansita',
          ),
        ),
      ],
    );
  }
}
