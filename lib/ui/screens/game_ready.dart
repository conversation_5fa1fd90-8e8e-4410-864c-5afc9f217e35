import 'package:flutter/material.dart';
import 'package:brain_game/enums/game_state.dart';
import 'package:brain_game/controllers/game_state_controller.dart';
import 'package:brain_game/controllers/game_mode_controller.dart';
import 'package:brain_game/utils/game_colors.dart';
import 'package:brain_game/utils/game_assets.dart';
import 'package:brain_game/ui/widgets/orientation_hint.dart';
import 'package:get/get.dart';

class GameReady extends StatefulWidget {
  const GameReady({Key? key}) : super(key: key);

  @override
  State<GameReady> createState() => _GameReadyState();
}

class _GameReadyState extends State<GameReady> with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  int _countdown = 3;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeOut));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeIn));

    _startCountdown();
  }

  void _startCountdown() async {
    await _controller.forward();

    for (int i = 3; i > 0; i--) {
      setState(() {
        _countdown = i;
      });
      await GameAssets.playClockTickingSound();
      await Future.delayed(const Duration(seconds: 1));
    }

    // Show orientation hint before starting game
    await _showOrientationHint();

    // Start game
    await GameAssets.playEarnCoinsSound();
    GameStateController.to.updateState(GameState.play);
    await GameAssets.backgroundPlayer.play();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Future<void> _showOrientationHint() async {
    await showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => const OrientationHint(),
    );
  }

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    double height = MediaQuery.of(context).size.height;

    return GetBuilder<GameModeController>(
      builder: (modeController) {
        final mode = modeController.selectedMode;

        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                mode.color.withOpacity(0.8),
                mode.color.withOpacity(0.6),
                GameColors.primary,
              ],
            ),
          ),
          child: SafeArea(
            child: AnimatedBuilder(
              animation: _controller,
              builder: (context, child) {
                return Transform.scale(
                  scale: _scaleAnimation.value,
                  child: Opacity(
                    opacity: _fadeAnimation.value,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Mode info
                        Container(
                          margin: const EdgeInsets.symmetric(horizontal: 30),
                          padding: const EdgeInsets.all(25),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.2),
                                blurRadius: 15,
                                offset: const Offset(0, 5),
                              ),
                            ],
                          ),
                          child: Column(
                            children: [
                              // Mode icon and name
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Container(
                                    padding: const EdgeInsets.all(15),
                                    decoration: BoxDecoration(
                                      color: mode.color.withOpacity(0.1),
                                      shape: BoxShape.circle,
                                    ),
                                    child: Icon(
                                      mode.icon,
                                      size: 40,
                                      color: mode.color,
                                    ),
                                  ),
                                  const SizedBox(width: 20),
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        mode.name,
                                        style: TextStyle(
                                          fontSize: 24,
                                          fontWeight: FontWeight.bold,
                                          color: mode.color,
                                          fontFamily: 'Sansita',
                                        ),
                                      ),
                                      Text(
                                        mode.description,
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: GameColors.grey.withOpacity(
                                            0.7,
                                          ),
                                          fontFamily: 'Sansita',
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),

                              const SizedBox(height: 25),

                              // Game stats
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceEvenly,
                                children: [
                                  _buildStatItem(
                                    'Levels',
                                    '${mode.levels}',
                                    Icons.layers,
                                  ),
                                  _buildStatItem(
                                    'Time',
                                    '${mode.timeLimit}ms',
                                    Icons.timer,
                                  ),
                                  _buildStatItem(
                                    'Swaps',
                                    '${mode.minSwaps}-${mode.maxSwaps}',
                                    Icons.swap_horiz,
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 50),

                        // Countdown
                        Container(
                          width: 120,
                          height: 120,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: mode.color.withOpacity(0.3),
                                blurRadius: 20,
                                offset: const Offset(0, 5),
                              ),
                            ],
                          ),
                          child: Center(
                            child: Text(
                              '$_countdown',
                              style: TextStyle(
                                fontSize: 48,
                                fontWeight: FontWeight.bold,
                                color: mode.color,
                                fontFamily: 'RubikMonoOne',
                              ),
                            ),
                          ),
                        ),

                        const SizedBox(height: 30),

                        Text(
                          'Chuẩn bị...',
                          style: TextStyle(
                            fontSize: 20,
                            color: Colors.white,
                            fontFamily: 'Sansita',
                            shadows: [
                              Shadow(
                                color: Colors.black.withOpacity(0.5),
                                offset: const Offset(1, 1),
                                blurRadius: 3,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, size: 24, color: GameColors.green),
        const SizedBox(height: 5),
        Text(
          value,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: GameColors.green,
            fontFamily: 'Sansita',
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: GameColors.grey.withOpacity(0.7),
            fontFamily: 'Sansita',
          ),
        ),
      ],
    );
  }
}
