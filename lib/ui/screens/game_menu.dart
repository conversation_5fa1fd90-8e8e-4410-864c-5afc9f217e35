import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:brain_game/enums/game_state.dart';
import 'package:brain_game/controllers/game_state_controller.dart';
import 'package:brain_game/controllers/game_mode_controller.dart';
import 'package:brain_game/models/game_mode.dart';
import 'package:brain_game/utils/game_colors.dart';
import 'package:brain_game/utils/game_assets.dart';
import 'package:get/get.dart';

class GameMenu extends StatefulWidget {
  const GameMenu({Key? key}) : super(key: key);

  @override
  State<GameMenu> createState() => _GameMenuState();
}

class _GameMenuState extends State<GameMenu> with TickerProviderStateMixin {
  late AnimationController _titleController;
  late AnimationController _menuController;
  late Animation<double> _titleAnimation;
  late Animation<double> _menuAnimation;

  @override
  void initState() {
    super.initState();

    _titleController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _menuController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _titleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _titleController, curve: Curves.elasticOut),
    );

    _menuAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _menuController, curve: Curves.easeOut));

    _startAnimations();
  }

  void _startAnimations() async {
    await _titleController.forward();
    await _menuController.forward();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _menuController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    double height = MediaQuery.of(context).size.height;

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [GameColors.primary, GameColors.secondary, GameColors.green],
        ),
      ),
      child: SafeArea(
        child: Column(
          children: [
            // Header với tiêu đề
            _buildHeader(width),

            // Menu chọn game
            Expanded(child: _buildGameModeGrid(width, height)),

            // Footer với các nút phụ
            _buildFooter(width),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(double width) {
    return AnimatedBuilder(
      animation: _titleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _titleAnimation.value,
          child: Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                // Logo hoặc icon game
                Container(
                  width: width * 0.2,
                  height: width * 0.2,
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.9),
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.3),
                        blurRadius: 15,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: Icon(
                    Icons.psychology,
                    size: width * 0.1,
                    color: GameColors.green,
                  ),
                ),
                const SizedBox(height: 15),

                // Game title
                Text(
                  'BRAIN GAME',
                  style: TextStyle(
                    fontSize: width * 0.08,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    fontFamily: 'RubikMonoOne',
                    shadows: [
                      Shadow(
                        color: Colors.black.withOpacity(0.5),
                        offset: const Offset(2, 2),
                        blurRadius: 4,
                      ),
                    ],
                  ),
                ),

                Text(
                  'Choose Difficulty',
                  style: TextStyle(
                    fontSize: width * 0.04,
                    color: Colors.white.withOpacity(0.9),
                    fontFamily: 'Sansita',
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildGameModeGrid(double width, double height) {
    return AnimatedBuilder(
      animation: _menuAnimation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, 50 * (1 - _menuAnimation.value)),
          child: Opacity(
            opacity: _menuAnimation.value,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: GridView.builder(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: 15,
                  mainAxisSpacing: 15,
                  childAspectRatio: 1.1,
                ),
                itemCount: GameMode.availableModes.length,
                itemBuilder: (context, index) {
                  final mode = GameMode.availableModes[index];
                  return _buildGameModeCard(mode, width);
                },
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildGameModeCard(GameMode mode, double width) {
    return GestureDetector(
      onTap: () => _selectGameMode(mode),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: mode.color.withOpacity(0.3),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Icon
              Container(
                width: width * 0.12,
                height: width * 0.12,
                decoration: BoxDecoration(
                  color: mode.color.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(mode.icon, size: width * 0.06, color: mode.color),
              ),

              const SizedBox(height: 8),

              // Tên mode
              Flexible(
                child: Text(
                  mode.name,
                  style: TextStyle(
                    fontSize: width * 0.04,
                    fontWeight: FontWeight.bold,
                    color: GameColors.grey,
                    fontFamily: 'Sansita',
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),

              const SizedBox(height: 4),

              // Mô tả
              Flexible(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 4),
                  child: Text(
                    mode.description,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: width * 0.022,
                      color: GameColors.grey.withOpacity(0.7),
                      fontFamily: 'Sansita',
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),

              const SizedBox(height: 6),

              // Thông tin level
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
                decoration: BoxDecoration(
                  color: mode.color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  '${mode.levels} levels',
                  style: TextStyle(
                    fontSize: width * 0.022,
                    color: mode.color,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFooter(double width) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildFooterButton(
            icon: Icons.arrow_back,
            label: 'Exit',
            onTap: () async {
              await GameAssets.playEarnCoinsSound();
              if (mounted) {
                _showExitDialog(context);
              }
            },
            width: width,
          ),
          _buildFooterButton(
            icon: Icons.settings,
            label: 'Settings',
            onTap: () async {
              await GameAssets.playEarnCoinsSound();
              GameStateController.to.updateState(GameState.settings);
            },
            width: width,
          ),
          _buildFooterButton(
            icon: Icons.leaderboard,
            label: 'Leaderboard',
            onTap: () async {
              await GameAssets.playEarnCoinsSound();
              GameStateController.to.updateState(GameState.highScores);
            },
            width: width,
          ),
        ],
      ),
    );
  }

  Widget _buildFooterButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    required double width,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.9),
          borderRadius: BorderRadius.circular(15),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: width * 0.06, color: GameColors.green),
            const SizedBox(height: 5),
            Text(
              label,
              style: TextStyle(
                fontSize: width * 0.025,
                color: GameColors.grey,
                fontFamily: 'Sansita',
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _selectGameMode(GameMode mode) async {
    // Save selected mode to controller
    GameModeController.to.selectMode(mode);

    // Sound effect
    await GameAssets.playEarnCoinsSound();

    // Switch to ready screen
    GameStateController.to.updateState(GameState.ready);
  }

  void _showExitDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            'Exit Game',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: GameColors.grey,
            ),
          ),
          content: const Text(
            'Are you sure you want to exit the game?',
            style: TextStyle(color: GameColors.grey),
          ),
          backgroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
              },
              child: const Text(
                'Cancel',
                style: TextStyle(
                  color: GameColors.green,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            TextButton(
              onPressed: () async {
                await GameAssets.playEarnCoinsSound();
                if (context.mounted) {
                  Navigator.of(context).pop(); // Close dialog
                }
                SystemNavigator.pop(); // Exit app
              },
              child: const Text(
                'Exit',
                style: TextStyle(
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
