import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:brain_game/controllers/settings_controller.dart';
import 'package:brain_game/controllers/game_state_controller.dart';
import 'package:brain_game/utils/game_colors.dart';
import 'package:brain_game/enums/game_state.dart';
import 'package:brain_game/utils/game_assets.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({Key? key}) : super(key: key);

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final TextEditingController _nameController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _nameController.text = SettingsController.to.settings.playerName;
  }

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              GameColors.primary,
              GameColors.secondary,
              GameColors.green,
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(),
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(20),
                  child: _buildSettingsContent(),
                ),
              ),
              _buildFooter(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          GestureDetector(
            onTap: () async {
              await GameAssets.playEarnCoinsSound();
              GameStateController.to.updateState(GameState.menu);
            },
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.9),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.2),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: const Icon(
                Icons.arrow_back,
                color: GameColors.green,
                size: 24,
              ),
            ),
          ),
          const SizedBox(width: 20),
          const Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Settings',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    fontFamily: 'Sansita',
                  ),
                ),
                Text(
                  'Customize your game experience',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.white70,
                    fontFamily: 'Sansita',
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsContent() {
    return GetBuilder<SettingsController>(
      builder: (controller) {
        return Column(
          children: [
            _buildPlayerSection(controller),
            const SizedBox(height: 20),
            _buildAudioSection(controller),
            const SizedBox(height: 20),
            _buildGameplaySection(controller),
            const SizedBox(height: 20),
            _buildOtherSection(controller),
          ],
        );
      },
    );
  }

  Widget _buildPlayerSection(SettingsController controller) {
    return _buildSection(
      title: 'Player Information',
      icon: Icons.person,
      children: [
        _buildTextFieldSetting(
          title: 'Player Name',
          controller: _nameController,
          onChanged: (value) => controller.updatePlayerName(value),
        ),
      ],
    );
  }

  Widget _buildAudioSection(SettingsController controller) {
    return _buildSection(
      title: 'Audio',
      icon: Icons.volume_up,
      children: [
        _buildSwitchSetting(
          title: 'Sound Effects',
          subtitle: 'Enable/disable sound effects',
          value: controller.settings.soundEnabled,
          onChanged: controller.updateSoundEnabled,
        ),
        _buildSliderSetting(
          title: 'Sound Effects Volume',
          value: controller.settings.soundVolume,
          onChanged:
              controller.settings.soundEnabled
                  ? controller.updateSoundVolume
                  : null,
        ),
        _buildSwitchSetting(
          title: 'Background Music',
          subtitle: 'Enable/disable background music',
          value: controller.settings.musicEnabled,
          onChanged: controller.updateMusicEnabled,
        ),
        _buildSliderSetting(
          title: 'Background Music Volume',
          value: controller.settings.musicVolume,
          onChanged:
              controller.settings.musicEnabled
                  ? controller.updateMusicVolume
                  : null,
        ),
      ],
    );
  }

  Widget _buildGameplaySection(SettingsController controller) {
    return _buildSection(
      title: 'Gameplay',
      icon: Icons.gamepad,
      children: [
        _buildSwitchSetting(
          title: 'Vibration',
          subtitle: 'Vibrate on important events',
          value: controller.settings.vibrationEnabled,
          onChanged: controller.updateVibrationEnabled,
        ),
        _buildSwitchSetting(
          title: 'Show Tutorial',
          subtitle: 'Show tutorial for new players',
          value: controller.settings.showTutorial,
          onChanged: controller.updateShowTutorial,
        ),
      ],
    );
  }

  Widget _buildOtherSection(SettingsController controller) {
    return _buildSection(
      title: 'Other',
      icon: Icons.settings,
      children: [
        _buildActionSetting(
          title: 'Reset to Default',
          subtitle: 'Reset all settings to default',
          icon: Icons.restore,
          onTap: () => _showResetDialog(controller),
        ),
        _buildInfoSetting(
          title: 'Version',
          subtitle: '1.0.0',
          icon: Icons.info_outline,
        ),
      ],
    );
  }

  Widget _buildSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.9),
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: GameColors.green.withOpacity(0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(15),
                topRight: Radius.circular(15),
              ),
            ),
            child: Row(
              children: [
                Icon(icon, color: GameColors.green, size: 24),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: GameColors.grey,
                    fontFamily: 'Sansita',
                  ),
                ),
              ],
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  Widget _buildSwitchSetting({
    required String title,
    required String subtitle,
    required bool value,
    required Function(bool) onChanged,
  }) {
    return ListTile(
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: GameColors.grey,
          fontFamily: 'Sansita',
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 14,
          color: GameColors.grey.withOpacity(0.7),
          fontFamily: 'Sansita',
        ),
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: GameColors.green,
      ),
    );
  }

  Widget _buildSliderSetting({
    required String title,
    required double value,
    required Function(double)? onChanged,
  }) {
    return ListTile(
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: GameColors.grey,
          fontFamily: 'Sansita',
        ),
      ),
      subtitle: Slider(
        value: value,
        onChanged: onChanged,
        activeColor: GameColors.green,
        inactiveColor: GameColors.grey.withOpacity(0.3),
        divisions: 10,
        label: '${(value * 100).round()}%',
      ),
    );
  }

  Widget _buildTextFieldSetting({
    required String title,
    required TextEditingController controller,
    required Function(String) onChanged,
  }) {
    return ListTile(
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: GameColors.grey,
          fontFamily: 'Sansita',
        ),
      ),
      subtitle: TextField(
        controller: controller,
        onChanged: onChanged,
        style: const TextStyle(
          fontSize: 16,
          color: GameColors.grey,
          fontFamily: 'Sansita',
        ),
        decoration: InputDecoration(
          hintText: 'Enter your name',
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: BorderSide(color: GameColors.grey.withOpacity(0.3)),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: const BorderSide(color: GameColors.green),
          ),
        ),
      ),
    );
  }

  Widget _buildActionSetting({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(icon, color: GameColors.green),
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: GameColors.grey,
          fontFamily: 'Sansita',
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 14,
          color: GameColors.grey.withOpacity(0.7),
          fontFamily: 'Sansita',
        ),
      ),
      trailing: const Icon(Icons.chevron_right, color: GameColors.grey),
      onTap: onTap,
    );
  }

  Widget _buildInfoSetting({
    required String title,
    required String subtitle,
    required IconData icon,
  }) {
    return ListTile(
      leading: Icon(icon, color: GameColors.green),
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: GameColors.grey,
          fontFamily: 'Sansita',
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 14,
          color: GameColors.grey.withOpacity(0.7),
          fontFamily: 'Sansita',
        ),
      ),
    );
  }

  Widget _buildFooter() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: ElevatedButton(
        onPressed: () async {
          await GameAssets.playEarnCoinsSound();
          GameStateController.to.updateState(GameState.menu);
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.white.withOpacity(0.9),
          foregroundColor: GameColors.green,
          padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 15),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(25),
          ),
        ),
        child: const Text(
          'Back to Menu',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            fontFamily: 'Sansita',
          ),
        ),
      ),
    );
  }

  void _showResetDialog(SettingsController controller) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
          title: const Text(
            'Reset to default settings?',
            style: TextStyle(
              fontFamily: 'Sansita',
              fontWeight: FontWeight.bold,
              color: GameColors.grey,
            ),
          ),
          content: SingleChildScrollView(
            child: const Text(
              'All settings will be reset to default. Are you sure?',
              style: TextStyle(fontFamily: 'Sansita', color: GameColors.grey),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'Cancel',
                style: TextStyle(fontFamily: 'Sansita', color: GameColors.grey),
              ),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await controller.resetToDefaults();
                _nameController.text = controller.settings.playerName;
                await GameAssets.playEarnCoinsSound();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: GameColors.green,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              child: const Text(
                'Reset',
                style: TextStyle(fontFamily: 'Sansita', color: Colors.white),
              ),
            ),
          ],
        );
      },
    );
  }
}
