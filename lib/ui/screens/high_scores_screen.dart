import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:brain_game/controllers/high_score_controller.dart';
import 'package:brain_game/controllers/game_state_controller.dart';
import 'package:brain_game/models/game_mode.dart';
import 'package:brain_game/utils/game_colors.dart';
import 'package:brain_game/enums/game_state.dart';
import 'package:brain_game/utils/game_assets.dart';

class HighScoresScreen extends StatefulWidget {
  const HighScoresScreen({Key? key}) : super(key: key);

  @override
  State<HighScoresScreen> createState() => _HighScoresScreenState();
}

class _HighScoresScreenState extends State<HighScoresScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              GameColors.primary,
              GameColors.secondary,
              GameColors.green,
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(),
              _buildTabBar(),
              Expanded(child: _buildTabBarView()),
              _buildFooter(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          GestureDetector(
            onTap: () async {
              await GameAssets.playEarnCoinsSound();
              GameStateController.to.updateState(GameState.menu);
            },
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.9),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.2),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: const Icon(
                Icons.arrow_back,
                color: GameColors.green,
                size: 24,
              ),
            ),
          ),
          const SizedBox(width: 20),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Leaderboard',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    fontFamily: 'Sansita',
                  ),
                ),
                Text(
                  'High Scores',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.white.withOpacity(0.8),
                    fontFamily: 'Sansita',
                  ),
                ),
              ],
            ),
          ),
          GestureDetector(
            onTap: () => _showClearDialog(),
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.8),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.2),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: const Icon(
                Icons.delete_outline,
                color: Colors.white,
                size: 24,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.9),
        borderRadius: BorderRadius.circular(25),
      ),
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        indicator: BoxDecoration(
          color: GameColors.green,
          borderRadius: BorderRadius.circular(25),
        ),
        labelColor: Colors.white,
        unselectedLabelColor: GameColors.grey,
        labelStyle: const TextStyle(
          fontFamily: 'Sansita',
          fontWeight: FontWeight.bold,
          fontSize: 12,
        ),
        tabs: const [
          Tab(text: 'All'),
          Tab(text: 'Easy'),
          Tab(text: 'Medium'),
          Tab(text: 'Hard'),
          Tab(text: 'Expert'),
        ],
      ),
    );
  }

  Widget _buildTabBarView() {
    return GetBuilder<HighScoreController>(
      builder: (controller) {
        return TabBarView(
          controller: _tabController,
          children: [
            _buildScoresList(controller.getTopScores()),
            _buildScoresList(
              controller.getScoresByDifficulty(GameDifficulty.easy),
            ),
            _buildScoresList(
              controller.getScoresByDifficulty(GameDifficulty.medium),
            ),
            _buildScoresList(
              controller.getScoresByDifficulty(GameDifficulty.hard),
            ),
            _buildScoresList(
              controller.getScoresByDifficulty(GameDifficulty.expert),
            ),
          ],
        );
      },
    );
  }

  Widget _buildScoresList(List scores) {
    if (scores.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.emoji_events_outlined,
              size: 80,
              color: Colors.white.withOpacity(0.5),
            ),
            const SizedBox(height: 20),
            Text(
              'No scores yet',
              style: TextStyle(
                fontSize: 18,
                color: Colors.white.withOpacity(0.8),
                fontFamily: 'Sansita',
              ),
            ),
            const SizedBox(height: 10),
            Text(
              'Play games to set records!',
              style: TextStyle(
                fontSize: 14,
                color: Colors.white.withOpacity(0.6),
                fontFamily: 'Sansita',
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(20),
      itemCount: scores.length,
      itemBuilder: (context, index) {
        final score = scores[index];
        return _buildScoreCard(score, index + 1);
      },
    );
  }

  Widget _buildScoreCard(score, int rank) {
    Color rankColor = GameColors.green;
    IconData rankIcon = Icons.emoji_events;

    if (rank == 1) {
      rankColor = const Color(0xFFFFD700); // Gold
      rankIcon = Icons.emoji_events;
    } else if (rank == 2) {
      rankColor = const Color(0xFFC0C0C0); // Silver
      rankIcon = Icons.emoji_events_outlined;
    } else if (rank == 3) {
      rankColor = const Color(0xFFCD7F32); // Bronze
      rankIcon = Icons.emoji_events_outlined;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.9),
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Rank
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: rankColor.withOpacity(0.2),
              shape: BoxShape.circle,
            ),
            child: Center(
              child:
                  rank <= 3
                      ? Icon(rankIcon, color: rankColor, size: 24)
                      : Text(
                        '$rank',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: rankColor,
                          fontFamily: 'Sansita',
                        ),
                      ),
            ),
          ),

          const SizedBox(width: 15),

          // Player info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  score.playerName,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: GameColors.grey,
                    fontFamily: 'Sansita',
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: GameColors.green.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Text(
                        score.difficultyName,
                        style: const TextStyle(
                          fontSize: 12,
                          color: GameColors.green,
                          fontFamily: 'Sansita',
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Level ${score.level}',
                      style: TextStyle(
                        fontSize: 12,
                        color: GameColors.grey.withOpacity(0.7),
                        fontFamily: 'Sansita',
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  score.formattedDate,
                  style: TextStyle(
                    fontSize: 12,
                    color: GameColors.grey.withOpacity(0.5),
                    fontFamily: 'Sansita',
                  ),
                ),
              ],
            ),
          ),

          // Score
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '${score.score}',
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: GameColors.green,
                  fontFamily: 'RubikMonoOne',
                ),
              ),
              Text(
                'points',
                style: TextStyle(
                  fontSize: 12,
                  color: GameColors.grey.withOpacity(0.7),
                  fontFamily: 'Sansita',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFooter() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: ElevatedButton(
        onPressed: () async {
          await GameAssets.playEarnCoinsSound();
          GameStateController.to.updateState(GameState.menu);
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.white.withOpacity(0.9),
          foregroundColor: GameColors.green,
          padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 15),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(25),
          ),
        ),
        child: const Text(
          'Back to Menu',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            fontFamily: 'Sansita',
          ),
        ),
      ),
    );
  }

  void _showClearDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
          title: const Text(
            'Delete all scores?',
            style: TextStyle(
              fontFamily: 'Sansita',
              fontWeight: FontWeight.bold,
              color: GameColors.grey,
            ),
          ),
          content: SingleChildScrollView(
            child: const Text(
              'Are you sure you want to delete all scores? This action cannot be undone.',
              style: TextStyle(fontFamily: 'Sansita', color: GameColors.grey),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'Cancel',
                style: TextStyle(fontFamily: 'Sansita', color: GameColors.grey),
              ),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await HighScoreController.to.clearAllScores();
                await GameAssets.playEarnCoinsSound();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              child: const Text(
                'Delete',
                style: TextStyle(fontFamily: 'Sansita', color: Colors.white),
              ),
            ),
          ],
        );
      },
    );
  }
}
