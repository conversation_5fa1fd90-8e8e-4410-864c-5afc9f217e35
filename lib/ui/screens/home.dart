import 'package:flutter/material.dart';
import 'package:brain_game/classes/driver.dart';
import 'package:brain_game/classes/item_selector.dart';
import 'package:brain_game/classes/swap_items_box.dart';
import 'package:brain_game/enums/game_state.dart';
import 'package:brain_game/enums/swap_item_name.dart';
import 'package:brain_game/enums/widget_name.dart';
import 'package:brain_game/controllers/game_state_controller.dart';
import 'package:brain_game/controllers/game_score_controller.dart';
import 'package:brain_game/controllers/game_mode_controller.dart';
import 'package:brain_game/controllers/high_score_controller.dart';
import 'package:brain_game/controllers/settings_controller.dart';
import 'package:brain_game/ui/widgets/alram_clock.dart';
import 'package:brain_game/ui/widgets/book.dart';
import 'package:brain_game/ui/widgets/light.dart';
import 'package:brain_game/ui/widgets/play.dart';
import 'package:brain_game/ui/widgets/score.dart';
import 'package:brain_game/ui/widgets/start.dart';
import 'package:brain_game/ui/screens/game_menu.dart';
import 'package:brain_game/ui/screens/game_ready.dart';
import 'package:brain_game/ui/screens/high_scores_screen.dart';
import 'package:brain_game/ui/screens/settings_screen.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:brain_game/utils/game_assets.dart';
import 'package:brain_game/utils/game_colors.dart';

class Home extends StatefulWidget {
  const Home({Key? key}) : super(key: key);

  @override
  State<Home> createState() => _HomeState();
}

class _HomeState extends State<Home> {
  late final Map<Enum, GlobalKey<State<StatefulWidget>>> _stateKeysMap;

  @override
  void initState() {
    super.initState();
    Get.put<GameScoreController>(GameScoreController());
    Get.put<GameStateController>(GameStateController());
    Get.put<GameModeController>(GameModeController());
    Get.put<HighScoreController>(HighScoreController());
    Get.put<SettingsController>(SettingsController());
    _stateKeysMap = {
      SwapItemName.firstSwapItem: GlobalKey<BookState>(),
      SwapItemName.secondSwapItem: GlobalKey<BookState>(),
      SwapItemName.thirdSwapItem: GlobalKey<BookState>(),
      WidgetName.score: GlobalKey<ScoreState>(),
      WidgetName.light: GlobalKey<LightState>(),
      WidgetName.alarmClock: GlobalKey<AlarmClockState>(),
    };
    Driver().setStateKeysMap = _stateKeysMap;
    SwapItemsBox().setSelectedSwapItem = ItemSelector.select();
  }

  @override
  void dispose() {
    GameAssets.backgroundPlayer.stop();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;
    double widthOfScreen = size.width;
    double heightOfScreen = size.height;

    return Scaffold(
      backgroundColor: GameColors.primary,
      body: GetBuilder<GameStateController>(
        builder: (gameStateController) {
          // Only use RotatedBox for game screen
          bool shouldRotate = gameStateController.state == GameState.play;
          bool isPortrait = heightOfScreen >= widthOfScreen;
          double widthOfGame =
              shouldRotate && isPortrait ? heightOfScreen : widthOfScreen;
          double heightOfGame =
              shouldRotate && isPortrait ? widthOfScreen : heightOfScreen;

          Widget content = SizedBox(
            width: widthOfGame,
            height: heightOfGame,
            child: AnimatedSwitcher(
              duration: const Duration(seconds: 1),
              switchInCurve: Curves.easeInOut,
              transitionBuilder: (child, animation) {
                return ScaleTransition(scale: animation, child: child);
              },
              child: _buildCurrentScreen(
                gameStateController.state,
                widthOfGame,
                heightOfGame,
              ),
            ),
          );

          // Only rotate for game screen in portrait mode
          if (shouldRotate && isPortrait) {
            return RotatedBox(quarterTurns: 1, child: content);
          } else {
            return content;
          }
        },
      ),
    );
  }

  Widget _buildCurrentScreen(
    GameState state,
    double widthOfGame,
    double heightOfGame,
  ) {
    // Set orientation based on current state
    _setOrientationForState(state);

    switch (state) {
      case GameState.menu:
        return const GameMenu();
      case GameState.ready:
        return const GameReady();
      case GameState.play:
        return Play(
          widthOfScreen: widthOfGame,
          heightOfScreen: heightOfGame,
          stateKeysMap: _stateKeysMap,
        );
      case GameState.highScores:
        return const HighScoresScreen();
      case GameState.settings:
        return const SettingsScreen();
    }
  }

  void _setOrientationForState(GameState state) {
    switch (state) {
      case GameState.menu:
      case GameState.ready:
      case GameState.highScores:
      case GameState.settings:
        SystemChrome.setPreferredOrientations([
          DeviceOrientation.portraitUp,
          DeviceOrientation.portraitDown,
        ]);
        break;
      case GameState.play:
        SystemChrome.setPreferredOrientations([
          DeviceOrientation.landscapeLeft,
          DeviceOrientation.landscapeRight,
          DeviceOrientation.portraitUp,
          DeviceOrientation.portraitDown,
        ]);
        break;
    }
  }
}
