import 'package:flutter/material.dart';

enum GameDifficulty { easy, medium, hard, expert }

class GameMode {
  final String name;
  final String description;
  final GameDifficulty difficulty;
  final Color color;
  final IconData icon;
  final int levels;
  final int timeLimit;
  final int minSwaps;
  final int maxSwaps;

  const GameMode({
    required this.name,
    required this.description,
    required this.difficulty,
    required this.color,
    required this.icon,
    required this.levels,
    required this.timeLimit,
    required this.minSwaps,
    required this.maxSwaps,
  });

  static const List<GameMode> availableModes = [
    GameMode(
      name: 'Easy',
      description: 'For beginners',
      difficulty: GameDifficulty.easy,
      color: Color(0xFF4CAF50),
      icon: Icons.sentiment_satisfied,
      levels: 5,
      timeLimit: 500,
      minSwaps: 3,
      maxSwaps: 5,
    ),
    GameMode(
      name: 'Medium',
      description: 'Moderate challenge',
      difficulty: GameDifficulty.medium,
      color: Color(0xFFFF9800),
      icon: Icons.sentiment_neutral,
      levels: 8,
      timeLimit: 400,
      minSwaps: 4,
      maxSwaps: 7,
    ),
    GameMode(
      name: 'Hard',
      description: 'For experienced players',
      difficulty: GameDifficulty.hard,
      color: Color(0xFFF44336),
      icon: Icons.sentiment_dissatisfied,
      levels: 10,
      timeLimit: 300,
      minSwaps: 5,
      maxSwaps: 9,
    ),
    GameMode(
      name: 'Expert',
      description: 'Ultimate challenge!',
      difficulty: GameDifficulty.expert,
      color: Color(0xFF9C27B0),
      icon: Icons.psychology,
      levels: 15,
      timeLimit: 250,
      minSwaps: 6,
      maxSwaps: 12,
    ),
  ];
}
