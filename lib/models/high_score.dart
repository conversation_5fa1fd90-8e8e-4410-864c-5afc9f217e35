import 'package:brain_game/models/game_mode.dart';

class HighScore {
  final String playerName;
  final int score;
  final GameDifficulty difficulty;
  final DateTime dateTime;
  final int level;
  final int timeRemaining;

  HighScore({
    required this.playerName,
    required this.score,
    required this.difficulty,
    required this.dateTime,
    required this.level,
    required this.timeRemaining,
  });

  Map<String, dynamic> toJson() {
    return {
      'playerName': playerName,
      'score': score,
      'difficulty': difficulty.index,
      'dateTime': dateTime.millisecondsSinceEpoch,
      'level': level,
      'timeRemaining': timeRemaining,
    };
  }

  factory HighScore.fromJson(Map<String, dynamic> json) {
    return HighScore(
      playerName: json['playerName'] ?? 'Unknown',
      score: json['score'] ?? 0,
      difficulty: GameDifficulty.values[json['difficulty'] ?? 0],
      dateTime: DateTime.fromMillisecondsSinceEpoch(json['dateTime'] ?? 0),
      level: json['level'] ?? 1,
      timeRemaining: json['timeRemaining'] ?? 0,
    );
  }

  String get difficultyName {
    switch (difficulty) {
      case GameDifficulty.easy:
        return 'Dễ';
      case GameDifficulty.medium:
        return 'Trung bình';
      case GameDifficulty.hard:
        return 'Khó';
      case GameDifficulty.expert:
        return 'Chuyên gia';
    }
  }

  String get formattedDate {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
  }
}
