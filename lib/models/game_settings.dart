class GameSettings {
  final bool soundEnabled;
  final bool musicEnabled;
  final bool vibrationEnabled;
  final double soundVolume;
  final double musicVolume;
  final String playerName;
  final bool showTutorial;
  final String language;

  const GameSettings({
    this.soundEnabled = true,
    this.musicEnabled = true,
    this.vibrationEnabled = true,
    this.soundVolume = 1.0,
    this.musicVolume = 0.7,
    this.playerName = 'Player',
    this.showTutorial = true,
    this.language = 'en',
  });

  GameSettings copyWith({
    bool? soundEnabled,
    bool? musicEnabled,
    bool? vibrationEnabled,
    double? soundVolume,
    double? musicVolume,
    String? playerName,
    bool? showTutorial,
    String? language,
  }) {
    return GameSettings(
      soundEnabled: soundEnabled ?? this.soundEnabled,
      musicEnabled: musicEnabled ?? this.musicEnabled,
      vibrationEnabled: vibrationEnabled ?? this.vibrationEnabled,
      soundVolume: soundVolume ?? this.soundVolume,
      musicVolume: musicVolume ?? this.musicVolume,
      playerName: playerName ?? this.playerName,
      showTutorial: showTutorial ?? this.showTutorial,
      language: language ?? this.language,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'soundEnabled': soundEnabled,
      'musicEnabled': musicEnabled,
      'vibrationEnabled': vibrationEnabled,
      'soundVolume': soundVolume,
      'musicVolume': musicVolume,
      'playerName': playerName,
      'showTutorial': showTutorial,
      'language': language,
    };
  }

  factory GameSettings.fromJson(Map<String, dynamic> json) {
    return GameSettings(
      soundEnabled: json['soundEnabled'] ?? true,
      musicEnabled: json['musicEnabled'] ?? true,
      vibrationEnabled: json['vibrationEnabled'] ?? true,
      soundVolume: (json['soundVolume'] ?? 1.0).toDouble(),
      musicVolume: (json['musicVolume'] ?? 0.7).toDouble(),
      playerName: json['playerName'] ?? 'Player',
      showTutorial: json['showTutorial'] ?? true,
      language: json['language'] ?? 'en',
    );
  }
}
