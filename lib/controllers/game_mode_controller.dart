import 'package:get/get.dart';
import 'package:brain_game/models/game_mode.dart';

class GameModeController extends GetxController {
  GameMode _selectedMode = GameMode.availableModes[0]; // Default to easy mode
  
  GameMode get selectedMode => _selectedMode;
  
  static GameModeController get to => Get.find<GameModeController>();
  
  void selectMode(GameMode mode) {
    _selectedMode = mode;
    update();
  }
  
  // Getter methods for easy access to current mode properties
  int get currentLevels => _selectedMode.levels;
  int get currentTimeLimit => _selectedMode.timeLimit;
  int get currentMinSwaps => _selectedMode.minSwaps;
  int get currentMaxSwaps => _selectedMode.maxSwaps;
  GameDifficulty get currentDifficulty => _selectedMode.difficulty;
  String get currentModeName => _selectedMode.name;
}
