import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:brain_game/models/game_settings.dart';
import 'package:brain_game/utils/game_assets.dart';

class SettingsController extends GetxController {
  GameSettings _settings = const GameSettings();
  
  GameSettings get settings => _settings;
  
  static SettingsController get to => Get.find<SettingsController>();

  @override
  void onInit() {
    super.onInit();
    loadSettings();
  }

  Future<void> loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString('game_settings');
      
      if (settingsJson != null) {
        _settings = GameSettings.fromJson(json.decode(settingsJson));
      }
      
      // Apply loaded settings
      await _applyAudioSettings();
      update();
    } catch (e) {
      print('Error loading settings: $e');
    }
  }

  Future<void> saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('game_settings', json.encode(_settings.toJson()));
      await _applyAudioSettings();
      update();
    } catch (e) {
      print('Error saving settings: $e');
    }
  }

  Future<void> updateSoundEnabled(bool enabled) async {
    _settings = _settings.copyWith(soundEnabled: enabled);
    await saveSettings();
  }

  Future<void> updateMusicEnabled(bool enabled) async {
    _settings = _settings.copyWith(musicEnabled: enabled);
    await saveSettings();
    
    if (!enabled) {
      await GameAssets.backgroundPlayer.stop();
    }
  }

  Future<void> updateVibrationEnabled(bool enabled) async {
    _settings = _settings.copyWith(vibrationEnabled: enabled);
    await saveSettings();
  }

  Future<void> updateSoundVolume(double volume) async {
    _settings = _settings.copyWith(soundVolume: volume);
    await saveSettings();
  }

  Future<void> updateMusicVolume(double volume) async {
    _settings = _settings.copyWith(musicVolume: volume);
    await saveSettings();
  }

  Future<void> updatePlayerName(String name) async {
    _settings = _settings.copyWith(playerName: name.trim().isEmpty ? 'Player' : name.trim());
    await saveSettings();
  }

  Future<void> updateShowTutorial(bool show) async {
    _settings = _settings.copyWith(showTutorial: show);
    await saveSettings();
  }

  Future<void> updateLanguage(String language) async {
    _settings = _settings.copyWith(language: language);
    await saveSettings();
  }

  Future<void> resetToDefaults() async {
    _settings = const GameSettings();
    await saveSettings();
  }

  Future<void> _applyAudioSettings() async {
    try {
      // Apply music volume
      await GameAssets.backgroundPlayer.setVolume(_settings.musicVolume);
      
      // Apply sound effects volume
      await GameAssets.turnOnPlayer.setVolume(_settings.soundVolume);
      await GameAssets.turnOffPlayer.setVolume(_settings.soundVolume);
      await GameAssets.clockTickingPlayer.setVolume(_settings.soundVolume);
      await GameAssets.gameOverPlayer.setVolume(_settings.soundVolume);
      await GameAssets.victoryPlayer.setVolume(_settings.soundVolume);
      await GameAssets.earnCoinsPlayer.setVolume(_settings.soundVolume);
    } catch (e) {
      print('Error applying audio settings: $e');
    }
  }

  // Helper methods for easy access
  bool get shouldPlaySounds => _settings.soundEnabled;
  bool get shouldPlayMusic => _settings.musicEnabled;
  bool get shouldVibrate => _settings.vibrationEnabled;
  String get playerName => _settings.playerName;
}
