import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:brain_game/models/high_score.dart';
import 'package:brain_game/models/game_mode.dart';

class HighScoreController extends GetxController {
  List<HighScore> _highScores = [];
  
  List<HighScore> get highScores => _highScores;
  
  static HighScoreController get to => Get.find<HighScoreController>();

  @override
  void onInit() {
    super.onInit();
    loadHighScores();
  }

  Future<void> loadHighScores() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final scoresJson = prefs.getStringList('high_scores') ?? [];
      
      _highScores = scoresJson
          .map((scoreStr) => HighScore.fromJson(json.decode(scoreStr)))
          .toList();
      
      // Sort by score descending
      _highScores.sort((a, b) => b.score.compareTo(a.score));
      update();
    } catch (e) {
      print('Error loading high scores: $e');
    }
  }

  Future<void> saveHighScore(HighScore score) async {
    try {
      _highScores.add(score);
      
      // Sort by score descending
      _highScores.sort((a, b) => b.score.compareTo(a.score));
      
      // Keep only top 50 scores
      if (_highScores.length > 50) {
        _highScores = _highScores.take(50).toList();
      }
      
      final prefs = await SharedPreferences.getInstance();
      final scoresJson = _highScores
          .map((score) => json.encode(score.toJson()))
          .toList();
      
      await prefs.setStringList('high_scores', scoresJson);
      update();
    } catch (e) {
      print('Error saving high score: $e');
    }
  }

  List<HighScore> getScoresByDifficulty(GameDifficulty difficulty) {
    return _highScores
        .where((score) => score.difficulty == difficulty)
        .take(10)
        .toList();
  }

  List<HighScore> getTopScores({int limit = 10}) {
    return _highScores.take(limit).toList();
  }

  bool isNewHighScore(int score, GameDifficulty difficulty) {
    final difficultyScores = getScoresByDifficulty(difficulty);
    if (difficultyScores.isEmpty) return true;
    if (difficultyScores.length < 10) return true;
    return score > difficultyScores.last.score;
  }

  int getRankForScore(int score, GameDifficulty difficulty) {
    final difficultyScores = getScoresByDifficulty(difficulty);
    int rank = 1;
    for (final highScore in difficultyScores) {
      if (score > highScore.score) break;
      rank++;
    }
    return rank;
  }

  Future<void> clearAllScores() async {
    try {
      _highScores.clear();
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('high_scores');
      update();
    } catch (e) {
      print('Error clearing high scores: $e');
    }
  }
}
